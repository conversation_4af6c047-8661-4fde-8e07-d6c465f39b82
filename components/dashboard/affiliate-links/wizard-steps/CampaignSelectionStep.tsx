"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FormField, FormItem, FormControl } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import { Globe, Plus } from "lucide-react"
import { memo } from "react"

// Sample campaign data - moved to separate file for better organization
const sampleCampaigns = [
  {
    id: "1",
    name: "Summer Fashion Collection",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/summer-fashion",
    commission: "5% per sale",
    status: "active",
  },
  {
    id: "2",
    name: "Tech Gadgets Promo",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/tech-gadgets",
    commission: "$10 per lead",
    status: "active",
  },
  {
    id: "3",
    name: "Home Decor Sale",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/home-decor",
    commission: "8% per sale",
    status: "active",
  },
  {
    id: "4",
    name: "Fitness Equipment",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/fitness",
    commission: "12% per sale",
    status: "active",
  },
]

interface CampaignSelectionStepProps {
  form: any
  selectedCampaign: any
  onCampaignSelect: (campaign: any) => void
}

export const CampaignSelectionStep = memo(function CampaignSelectionStep({
  form,
  selectedCampaign,
  onCampaignSelect,
}: CampaignSelectionStepProps) {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="campaignId"
        render={({ field }) => (
          <FormItem className="hidden">
            <FormControl>
              <Input {...field} />
            </FormControl>
          </FormItem>
        )}
      />

      <div className="grid gap-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Select a Campaign</h3>
          <Button variant="outline" size="sm">
            <Plus className="mr-1 h-3 w-3" />
            New Campaign
          </Button>
        </div>

        <div className="grid gap-3">
          {sampleCampaigns.map((campaign) => (
            <div
              key={campaign.id}
              className={cn(
                "flex cursor-pointer items-center gap-3 rounded-lg border p-3 transition-colors hover:bg-accent",
                selectedCampaign?.id === campaign.id && "border-primary bg-primary/5",
              )}
              onClick={() => onCampaignSelect(campaign)}
            >
              <div className="flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded-full bg-muted">
                <img
                  src={campaign.logo || "/placeholder.svg"}
                  alt={campaign.name}
                  className="h-full w-full object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{campaign.name}</h4>
                  <Badge variant="outline" className="capitalize">
                    {campaign.status}
                  </Badge>
                </div>
                <div className="mt-1 flex items-center gap-3 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Globe className="h-3 w-3" />
                    {campaign.baseUrl}
                  </span>
                  <Separator orientation="vertical" className="h-3" />
                  <span>{campaign.commission}</span>
                </div>
              </div>
              <RadioGroup value={selectedCampaign?.id === campaign.id ? "selected" : ""}>
                <RadioGroupItem value="selected" id={`campaign-${campaign.id}`} />
              </RadioGroup>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
})

export { sampleCampaigns }
