"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { ChevronLeft, ChevronRight, Facebook, Instagram, Linkedin, Twitter } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Sample calendar events data
const calendarEvents = [
  {
    id: 1,
    title: "Product Launch Announcement",
    date: new Date(2023, 6, 5),
    time: "10:00 AM",
    platform: "Facebook",
    type: "Image",
    status: "Scheduled",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 2,
    title: "Customer Success Story",
    date: new Date(2023, 6, 5),
    time: "2:00 PM",
    platform: "LinkedIn",
    type: "Article",
    status: "Scheduled",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 3,
    title: "Behind the Scenes Video",
    date: new Date(2023, 6, 8),
    time: "11:30 AM",
    platform: "Instagram",
    type: "Video",
    status: "Scheduled",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 4,
    title: "Industry News Update",
    date: new Date(2023, 6, 10),
    time: "9:00 AM",
    platform: "Twitter",
    type: "Link",
    status: "Scheduled",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 5,
    title: "Weekly Tips and Tricks",
    date: new Date(2023, 6, 12),
    time: "3:00 PM",
    platform: "Instagram",
    type: "Carousel",
    status: "Scheduled",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 6,
    title: "New Feature Announcement",
    date: new Date(2023, 6, 15),
    time: "10:00 AM",
    platform: "Facebook",
    type: "Image",
    status: "Scheduled",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 7,
    title: "Team Spotlight",
    date: new Date(2023, 6, 18),
    time: "1:00 PM",
    platform: "LinkedIn",
    type: "Article",
    status: "Draft",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 8,
    title: "Product Demo",
    date: new Date(2023, 6, 20),
    time: "2:30 PM",
    platform: "Instagram",
    type: "Video",
    status: "Draft",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 9,
    title: "Customer Testimonial",
    date: new Date(2023, 6, 22),
    time: "11:00 AM",
    platform: "Twitter",
    type: "Quote",
    status: "Draft",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 10,
    title: "Holiday Special Promotion",
    date: new Date(2023, 6, 25),
    time: "9:30 AM",
    platform: "Facebook",
    type: "Image",
    status: "Draft",
    image: "/placeholder.svg?height=100&width=100",
  },
]

export function ContentCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [selectedEvent, setSelectedEvent] = useState<any | null>(null)
  const [eventDetailsOpen, setEventDetailsOpen] = useState(false)

  // Function to get platform icon
  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case "facebook":
        return <Facebook className="h-4 w-4 text-blue-600" />
      case "instagram":
        return <Instagram className="h-4 w-4 text-pink-600" />
      case "twitter":
        return <Twitter className="h-4 w-4 text-blue-400" />
      case "linkedin":
        return <Linkedin className="h-4 w-4 text-blue-800" />
      default:
        return null
    }
  }

  // Function to get events for a specific date
  const getEventsForDate = (date: Date | undefined | null) => {
    if (!date) return []

    return calendarEvents.filter(
      (event) =>
        event.date.getDate() === date.getDate() &&
        event.date.getMonth() === date.getMonth() &&
        event.date.getFullYear() === date.getFullYear(),
    )
  }

  // Function to handle event click
  const handleEventClick = (event: any) => {
    setSelectedEvent(event)
    setEventDetailsOpen(true)
  }

  // Function to render calendar day cell
  const renderDay = (day: Date | undefined | null) => {
    if (!day) return <div className="h-full w-full p-1"></div>

    const events = getEventsForDate(day)
    return (
      <div className="h-full w-full p-1">
        <div className="text-right text-sm">{day.getDate()}</div>
        <div className="mt-1 space-y-1">
          {events.map((event) => (
            <div
              key={event.id}
              className="cursor-pointer rounded-md p-1 text-xs hover:bg-muted"
              onClick={() => handleEventClick(event)}
            >
              <div className="flex items-center gap-1">
                {getPlatformIcon(event.platform)}
                <span className="line-clamp-1">{event.title}</span>
              </div>
              <div className="text-muted-foreground">{event.time}</div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="px-6 pb-6">
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center justify-between border-b p-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newDate = new Date(date!)
                  newDate.setMonth(newDate.getMonth() - 1)
                  setDate(newDate)
                }}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-lg font-semibold">
                {date?.toLocaleDateString("en-US", {
                  month: "long",
                  year: "numeric",
                })}
              </h2>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newDate = new Date(date!)
                  newDate.setMonth(newDate.getMonth() + 1)
                  setDate(newDate)
                }}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <Button variant="outline" onClick={() => setDate(new Date())}>
              Today
            </Button>
          </div>
          <div className="h-[600px] overflow-auto">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              className="w-full"
              components={{
                Day: (props) => (
                  <div {...props} className="h-24 w-full overflow-hidden border p-0">
                    {renderDay(props.date)}
                  </div>
                ),
              }}
            />
          </div>
        </CardContent>
      </Card>

      <Dialog open={eventDetailsOpen} onOpenChange={setEventDetailsOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Content Details</DialogTitle>
            <DialogDescription>View and manage scheduled content</DialogDescription>
          </DialogHeader>
          {selectedEvent && (
            <div className="space-y-4">
              <div className="flex gap-4">
                <div className="h-20 w-20 overflow-hidden rounded-md bg-muted">
                  <img
                    src={selectedEvent.image || "/placeholder.svg"}
                    alt={selectedEvent.title}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">{selectedEvent.title}</h3>
                  <div className="mt-1 flex items-center gap-2">
                    <Badge variant="outline">{selectedEvent.platform}</Badge>
                    <Badge variant="outline">{selectedEvent.type}</Badge>
                    <Badge variant={selectedEvent.status === "Scheduled" ? "default" : "secondary"}>
                      {selectedEvent.status}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Date</p>
                  <p className="font-medium">{selectedEvent.date.toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Time</p>
                  <p className="font-medium">{selectedEvent.time}</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Caption</p>
                <p className="text-sm">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et
                  dolore magna aliqua.
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Created By</p>
                <div className="mt-1 flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src="/placeholder.svg?height=24&width=24" alt="User" />
                    <AvatarFallback>SM</AvatarFallback>
                  </Avatar>
                  <span className="text-sm">Sarah Miller</span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEventDetailsOpen(false)}>
              Close
            </Button>
            <Button>Edit Content</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
