"use client"

import { Check, Facebook, Instagram, Linkedin, Twitter, Youtube } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useState } from "react"

const platforms = [
  {
    value: "all",
    label: "All Platforms",
    icon: null,
  },
  {
    value: "facebook",
    label: "Facebook",
    icon: Facebook,
  },
  {
    value: "instagram",
    label: "Instagram",
    icon: Instagram,
  },
  {
    value: "twitter",
    label: "Twitter",
    icon: Twitter,
  },
  {
    value: "linkedin",
    label: "LinkedIn",
    icon: Linkedin,
  },
  {
    value: "youtube",
    label: "YouTube",
    icon: Youtube,
  },
]

interface PlatformSelectorProps {
  selectedPlatforms?: string[]
  onPlatformsChange?: (platforms: string[]) => void
}

export function PlatformSelector({ selectedPlatforms: externalSelectedPlatforms, onPlatformsChange }: PlatformSelectorProps = {}) {
  const [open, setOpen] = useState(false)
  const [internalSelectedPlatforms, setInternalSelectedPlatforms] = useState<string[]>(["all"])

  // Use external state if provided, otherwise use internal state
  const selectedPlatforms = externalSelectedPlatforms || internalSelectedPlatforms
  const setSelectedPlatforms = onPlatformsChange || setInternalSelectedPlatforms

  const togglePlatform = (platform: string) => {
    if (platform === "all") {
      setSelectedPlatforms(["all"])
      return
    }

    let newSelection = [...selectedPlatforms]

    // Remove "all" if it's selected
    if (newSelection.includes("all")) {
      newSelection = newSelection.filter((p) => p !== "all")
    }

    // Toggle the selected platform
    if (newSelection.includes(platform)) {
      newSelection = newSelection.filter((p) => p !== platform)
      // If nothing is selected, default to "all"
      if (newSelection.length === 0) {
        newSelection = ["all"]
      }
    } else {
      newSelection.push(platform)
    }

    setSelectedPlatforms(newSelection)
  }

  const selectedLabels = selectedPlatforms
    .map((value) => {
      const platform = platforms.find((p) => p.value === value)
      return platform?.label
    })
    .join(", ")

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" aria-expanded={open} className="w-[280px] justify-between">
          {selectedLabels}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="ml-2 h-4 w-4 shrink-0 opacity-50"
          >
            <path d="m7 15 5 5 5-5" />
            <path d="m7 9 5-5 5 5" />
          </svg>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[280px] p-0">
        <Command>
          <CommandInput placeholder="Search platforms..." />
          <CommandList>
            <CommandEmpty>No platform found.</CommandEmpty>
            <CommandGroup>
              {platforms.map((platform) => (
                <CommandItem
                  key={platform.value}
                  value={platform.value}
                  onSelect={() => {
                    togglePlatform(platform.value)
                  }}
                >
                  <div
                    className={cn(
                      "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                      selectedPlatforms.includes(platform.value)
                        ? "bg-primary text-primary-foreground"
                        : "opacity-50 [&_svg]:invisible",
                    )}
                  >
                    <Check className="h-4 w-4" />
                  </div>
                  {platform.icon && <platform.icon className="mr-2 h-4 w-4 text-muted-foreground" />}
                  <span>{platform.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                onSelect={() => {
                  setOpen(false)
                }}
                className="justify-center text-center"
              >
                <Button variant="ghost" className="w-full">
                  Apply
                </Button>
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
