"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

const ageData = [
  { name: "18-24", value: 25 },
  { name: "25-34", value: 35 },
  { name: "35-44", value: 20 },
  { name: "45-54", value: 12 },
  { name: "55+", value: 8 },
]

const genderData = [
  { name: "Male", value: 45 },
  { name: "Female", value: 52 },
  { name: "Other", value: 3 },
]

const locationData = [
  { name: "United States", value: 40 },
  { name: "United Kingdom", value: 15 },
  { name: "Canada", value: 12 },
  { name: "Australia", value: 8 },
  { name: "Germany", value: 6 },
  { name: "Other", value: 19 },
]

const COLORS = ["bg-blue-500", "bg-green-500", "bg-yellow-500", "bg-red-500", "bg-purple-500", "bg-pink-500"]

export function AudienceDemographics() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Demographics</CardTitle>
        <CardDescription>Understand your audience composition</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="age">
          <TabsList className="mb-4">
            <TabsTrigger value="age">Age</TabsTrigger>
            <TabsTrigger value="gender">Gender</TabsTrigger>
            <TabsTrigger value="location">Location</TabsTrigger>
          </TabsList>

          <TabsContent value="age">
            <div className="space-y-4">
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold">Age Distribution</h3>
                <p className="text-sm text-muted-foreground">Breakdown by age groups</p>
              </div>
              <div className="space-y-3">
                {ageData.map((item, index) => (
                  <div key={item.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full ${COLORS[index % COLORS.length]}`} />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Progress value={item.value} className="w-24 h-2" />
                      <Badge variant="secondary">{item.value}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="gender">
            <div className="space-y-4">
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold">Gender Distribution</h3>
                <p className="text-sm text-muted-foreground">Breakdown by gender</p>
              </div>
              <div className="space-y-3">
                {genderData.map((item, index) => (
                  <div key={item.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full ${COLORS[index % COLORS.length]}`} />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Progress value={item.value} className="w-24 h-2" />
                      <Badge variant="secondary">{item.value}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="location">
            <div className="space-y-4">
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold">Geographic Distribution</h3>
                <p className="text-sm text-muted-foreground">Breakdown by location</p>
              </div>
              <div className="space-y-3">
                {locationData.map((item, index) => (
                  <div key={item.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full ${COLORS[index % COLORS.length]}`} />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Progress value={item.value} className="w-24 h-2" />
                      <Badge variant="secondary">{item.value}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
