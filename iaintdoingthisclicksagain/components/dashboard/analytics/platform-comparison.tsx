"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

const platformData = [
  {
    name: "<PERSON>",
    facebook: 4000,
    instagram: 2400,
    twitter: 2400,
    linkedin: 1800,
  },
  {
    name: "Feb",
    facebook: 3000,
    instagram: 1398,
    twitter: 2210,
    linkedin: 1600,
  },
  {
    name: "Mar",
    facebook: 2000,
    instagram: 9800,
    twitter: 2290,
    linkedin: 2100,
  },
  {
    name: "Apr",
    facebook: 2780,
    instagram: 3908,
    twitter: 2000,
    linkedin: 2400,
  },
  {
    name: "May",
    facebook: 1890,
    instagram: 4800,
    twitter: 2181,
    linkedin: 2200,
  },
  {
    name: "Jun",
    facebook: 2390,
    instagram: 3800,
    twitter: 2500,
    linkedin: 2300,
  },
  {
    name: "Jul",
    facebook: 3490,
    instagram: 4300,
    twitter: 2100,
    linkedin: 2500,
  },
]

export function PlatformComparison() {
  // Calculate platform totals and averages
  const platforms = [
    {
      name: "Facebook",
      total: platformData.reduce((sum, item) => sum + item.facebook, 0),
      color: "bg-blue-500",
      textColor: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      name: "Instagram",
      total: platformData.reduce((sum, item) => sum + item.instagram, 0),
      color: "bg-pink-500",
      textColor: "text-pink-600",
      bgColor: "bg-pink-50",
    },
    {
      name: "Twitter",
      total: platformData.reduce((sum, item) => sum + item.twitter, 0),
      color: "bg-sky-500",
      textColor: "text-sky-600",
      bgColor: "bg-sky-50",
    },
    {
      name: "LinkedIn",
      total: platformData.reduce((sum, item) => sum + item.linkedin, 0),
      color: "bg-blue-700",
      textColor: "text-blue-700",
      bgColor: "bg-blue-50",
    },
  ];

  const maxTotal = Math.max(...platforms.map(p => p.total));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Comparison</CardTitle>
        <CardDescription>Compare performance across social media platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {platforms.map((platform) => (
            <div key={platform.name} className={`p-4 rounded-lg ${platform.bgColor}`}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${platform.color}`} />
                  <span className="font-medium">{platform.name}</span>
                </div>
                <Badge variant="secondary" className={platform.textColor}>
                  {platform.total.toLocaleString()}
                </Badge>
              </div>
              <Progress
                value={(platform.total / maxTotal) * 100}
                className="h-3"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>Avg: {Math.round(platform.total / platformData.length).toLocaleString()}</span>
                <span>{((platform.total / maxTotal) * 100).toFixed(1)}% of top performer</span>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-pink-600">
              {Math.max(...platforms.map(p => p.total)).toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">Top Platform Total</p>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {platforms.reduce((sum, p) => sum + p.total, 0).toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">Combined Total</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
