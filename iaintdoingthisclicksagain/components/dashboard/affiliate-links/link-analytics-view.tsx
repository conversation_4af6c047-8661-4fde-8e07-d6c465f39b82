"use client"
import { useEffect, useState } from "react"
import { Activity, ArrowLeft, Calendar, Download, ExternalLink, Globe, Share2, Zap } from "lucide-react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"
import { format, subDays } from "date-fns"
import { toast, Toaster } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

import { useRealTimeAnalytics } from "@/hooks/use-realtime-analytics"
import { RealTimeActivityFeed } from "./real-time-activity-feed"
import { LiveMetricsCards } from "./live-metrics-cards"

// Sample data for the analytics
const sampleLinkData = {
  id: "1",
  name: "Summer Fashion Collection - Instagram",
  campaign: "Summer Fashion Collection",
  link: "https://example.com/ref/user123/summer-fashion?utm_source=instagram&utm_medium=social&utm_campaign=summer-2024",
  shortLink: "https://short.ly/abc123",
  status: "active",
  createdAt: "2024-01-15",
  totalClicks: 12450,
  uniqueClicks: 8920,
  conversions: 234,
  revenue: 2340.5,
  conversionRate: 2.62,
  clicksChange: 15.2,
  conversionsChange: 8.7,
  revenueChange: 12.3,
}

// Time series data for charts
const clicksOverTime = Array.from({ length: 30 }, (_, i) => ({
  date: format(subDays(new Date(), 29 - i), "MMM dd"),
  clicks: Math.floor(Math.random() * 500) + 100,
  uniqueClicks: Math.floor(Math.random() * 300) + 50,
  conversions: Math.floor(Math.random() * 20) + 2,
}))

// Geographic data
const geographicData = [
  { country: "United States", clicks: 4200, percentage: 33.7 },
  { country: "United Kingdom", clicks: 2100, percentage: 16.9 },
  { country: "Canada", clicks: 1800, percentage: 14.5 },
  { country: "Australia", clicks: 1200, percentage: 9.6 },
  { country: "Germany", clicks: 900, percentage: 7.2 },
  { country: "France", clicks: 750, percentage: 6.0 },
  { country: "Others", clicks: 1500, percentage: 12.1 },
]

// Device breakdown
const deviceData = [
  { name: "Mobile", value: 7200, percentage: 57.8, color: "#8884d8" },
  { name: "Desktop", value: 3600, percentage: 28.9, color: "#82ca9d" },
  { name: "Tablet", value: 1650, percentage: 13.3, color: "#ffc658" },
]

// Referrer data
const referrerData = [
  { source: "Instagram", clicks: 5200, conversions: 98, conversionRate: 1.88 },
  { source: "Facebook", clicks: 3100, conversions: 67, conversionRate: 2.16 },
  { source: "Twitter", clicks: 2400, conversions: 42, conversionRate: 1.75 },
  { source: "Direct", clicks: 1200, conversions: 18, conversionRate: 1.5 },
  { source: "Email", clicks: 550, conversions: 9, conversionRate: 1.64 },
]

interface LinkAnalyticsViewProps {
  linkId: string
}

export function LinkAnalyticsView({ linkId }: LinkAnalyticsViewProps) {
  const [dateRange, setDateRange] = useState("30d")
  const [activeTab, setActiveTab] = useState("overview")
  const [showConnectionInfo, setShowConnectionInfo] = useState(false)

  // Get real-time analytics data through WebSocket
  const {
    data: liveData,
    status,
    isConnected,
    notificationsEnabled,
    previousMetrics,
    startRealTime,
    stopRealTime,
    toggleNotifications,
    simulateClick,
  } = useRealTimeAnalytics(linkId, true)

  // Effect to show connection status changes
  useEffect(() => {
    if (status === "connected") {
      toast.success("WebSocket connected", {
        description: "Real-time data stream established",
      })
      setShowConnectionInfo(true)
      setTimeout(() => setShowConnectionInfo(false), 5000)
    } else if (status === "connecting") {
      toast.loading("Connecting to WebSocket...", {
        description: "Establishing real-time data stream",
      })
    } else if (status === "error") {
      toast.error("WebSocket connection error", {
        description: "Unable to establish real-time data stream",
      })
      setShowConnectionInfo(true)
    } else if (status === "disconnected") {
      // Only show if previously connected
      if (isConnected) {
        toast.info("WebSocket disconnected", {
          description: "Real-time data stream closed",
        })
      }
    }
  }, [status, isConnected])

  const handleExportData = () => {
    // Simulate data export
    const exportData = {
      linkData: sampleLinkData,
      clicksOverTime,
      geographicData,
      deviceData,
      referrerData,
      liveMetrics: liveData.metrics,
      timestamp: new Date().toISOString(),
    }

    const jsonData = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonData], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = `link-analytics-${linkId}-${format(new Date(), "yyyy-MM-dd")}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success("Analytics data exported", {
      description: `File: link-analytics-${linkId}-${format(new Date(), "yyyy-MM-dd")}.json`,
    })
  }

  return (
    <div className="space-y-6 p-6">
      <Toaster position="top-right" />

      {/* Connection status alert */}
      {showConnectionInfo && (
        <Alert
          variant={status === "connected" ? "default" : status === "error" ? "destructive" : "outline"}
          className="transition-all duration-500"
        >
          <Zap className="h-4 w-4" />
          <AlertTitle>
            {status === "connected"
              ? "WebSocket Connected"
              : status === "error"
                ? "Connection Error"
                : "Connection Status"}
          </AlertTitle>
          <AlertDescription>
            {status === "connected"
              ? "You're receiving real-time analytics data from the server."
              : status === "error"
                ? "Unable to connect to the real-time data stream. Using fallback data."
                : "Connection status: " + status}
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => window.history.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{sampleLinkData.name}</h1>
            <p className="text-sm text-muted-foreground">
              Campaign: {sampleLinkData.campaign} • Created {format(new Date(sampleLinkData.createdAt), "MMM dd, yyyy")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[140px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" onClick={() => window.open(sampleLinkData.link, "_blank")}>
            <ExternalLink className="mr-2 h-4 w-4" />
            Visit Link
          </Button>
          <Button
            variant={isConnected ? "default" : "outline"}
            onClick={isConnected ? stopRealTime : startRealTime}
            className="h-9"
          >
            {isConnected ? (
              <>
                <div className="mr-2 h-2 w-2 animate-pulse rounded-full bg-green-500" />
                Live
              </>
            ) : (
              <>
                <Activity className="mr-2 h-4 w-4" />
                Connect
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Link Info Card */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant={sampleLinkData.status === "active" ? "default" : "secondary"}>
                  {sampleLinkData.status}
                </Badge>
                <span className="text-sm text-muted-foreground">Status</span>
                {isConnected && (
                  <Badge variant="outline" className="ml-2 gap-1 pl-1">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span> WebSocket Connected
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" onClick={simulateClick}>
                  <Zap className="mr-2 h-4 w-4" />
                  Simulate Click
                </Button>
                <Button variant="ghost" size="sm">
                  <Share2 className="mr-2 h-4 w-4" />
                  Share Analytics
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Full Link</div>
              <div className="break-all rounded bg-muted p-2 text-sm font-mono">{sampleLinkData.link}</div>
            </div>
            {sampleLinkData.shortLink && (
              <div className="space-y-2">
                <div className="text-sm font-medium">Short Link</div>
                <div className="break-all rounded bg-muted p-2 text-sm font-mono">{sampleLinkData.shortLink}</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics - Using LiveMetricsCards component */}
      <LiveMetricsCards
        metrics={liveData.metrics}
        isLive={isConnected}
        previousMetrics={previousMetrics || undefined} // Track changes between updates
      />

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="traffic">Traffic</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="referrers">Referrers</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Clicks Over Time</CardTitle>
                <CardDescription>Daily clicks and conversions for the selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    clicks: { label: "Clicks", color: "hsl(var(--primary))" },
                    conversions: { label: "Conversions", color: "hsl(var(--destructive))" },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={clicksOverTime}>
                      <XAxis dataKey="date" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Area
                        type="monotone"
                        dataKey="clicks"
                        stackId="1"
                        stroke="hsl(var(--primary))"
                        fill="hsl(var(--primary))"
                        fillOpacity={0.6}
                      />
                      <Area
                        type="monotone"
                        dataKey="conversions"
                        stackId="2"
                        stroke="hsl(var(--destructive))"
                        fill="hsl(var(--destructive))"
                        fillOpacity={0.6}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
                <CardDescription>User journey from click to conversion</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Total Clicks</span>
                    <span className="text-sm font-bold">{liveData.metrics.totalClicks.toLocaleString()}</span>
                  </div>
                  <Progress value={100} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Unique Visitors</span>
                    <span className="text-sm font-bold">{liveData.metrics.uniqueClicks.toLocaleString()}</span>
                  </div>
                  <Progress
                    value={(liveData.metrics.uniqueClicks / liveData.metrics.totalClicks) * 100}
                    className="h-2"
                  />

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Engaged Users</span>
                    <span className="text-sm font-bold">6,240</span>
                  </div>
                  <Progress value={50.1} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Conversions</span>
                    <span className="text-sm font-bold">{liveData.metrics.conversions}</span>
                  </div>
                  <Progress value={liveData.metrics.conversionRate} className="h-2" />
                </div>

                <div className="pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{liveData.metrics.conversionRate.toFixed(2)}%</div>
                    <div className="text-sm text-muted-foreground">Conversion Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <RealTimeActivityFeed
              clicks={liveData.recentClicks}
              isLive={isConnected}
              lastUpdate={liveData.lastUpdate}
              notificationsEnabled={notificationsEnabled}
              onToggleNotifications={toggleNotifications}
            />
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Performance Summary</CardTitle>
              <CardDescription>Key performance indicators for this link</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Average Revenue per Click</div>
                  <div className="text-2xl font-bold">
                    ${(liveData.metrics.revenue / liveData.metrics.totalClicks).toFixed(3)}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Average Revenue per Conversion</div>
                  <div className="text-2xl font-bold">
                    ${(liveData.metrics.revenue / liveData.metrics.conversions).toFixed(2)}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">Click-through Rate</div>
                  <div className="text-2xl font-bold">
                    {((liveData.metrics.uniqueClicks / liveData.metrics.totalClicks) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Traffic Tab */}
        <TabsContent value="traffic" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Hourly Click Pattern</CardTitle>
                <CardDescription>Click distribution throughout the day</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    clicks: { label: "Clicks", color: "hsl(var(--primary))" },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={liveData.hourlyData}>
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="clicks" fill="hsl(var(--primary))" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Traffic Quality Metrics</CardTitle>
                <CardDescription>Engagement and quality indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Bounce Rate</span>
                    <span className="text-sm font-bold">32.4%</span>
                  </div>
                  <Progress value={32.4} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Average Session Duration</span>
                    <span className="text-sm font-bold">2m 34s</span>
                  </div>
                  <Progress value={65} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Return Visitor Rate</span>
                    <span className="text-sm font-bold">18.7%</span>
                  </div>
                  <Progress value={18.7} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Mobile Traffic</span>
                    <span className="text-sm font-bold">57.8%</span>
                  </div>
                  <Progress value={57.8} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Geography Tab */}
        <TabsContent value="geography" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
              <CardDescription>Clicks by country and region</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Country</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Percentage</TableHead>
                    <TableHead>Distribution</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {geographicData.map((country) => (
                    <TableRow key={country.country}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          {country.country}
                        </div>
                      </TableCell>
                      <TableCell>{country.clicks.toLocaleString()}</TableCell>
                      <TableCell>{country.percentage}%</TableCell>
                      <TableCell>
                        <Progress value={country.percentage} className="h-2 w-20" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Devices Tab */}
        <TabsContent value="devices" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Device Breakdown</CardTitle>
                <CardDescription>Clicks by device type</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    mobile: { label: "Mobile", color: "#8884d8" },
                    desktop: { label: "Desktop", color: "#82ca9d" },
                    tablet: { label: "Tablet", color: "#ffc658" },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={deviceData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percentage }) => `${name} ${percentage}%`}
                      >
                        {deviceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Device Performance</CardTitle>
                <CardDescription>Conversion rates by device type</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {deviceData.map((device) => (
                  <div key={device.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{device.name}</span>
                      <span className="text-sm font-bold">{device.value.toLocaleString()} clicks</span>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{device.percentage}% of total</span>
                      <span>2.{Math.floor(Math.random() * 9)}% conversion rate</span>
                    </div>
                    <Progress value={device.percentage} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Referrers Tab */}
        <TabsContent value="referrers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Traffic Sources</CardTitle>
              <CardDescription>Clicks and conversions by referrer</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Source</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Conversions</TableHead>
                    <TableHead>Conversion Rate</TableHead>
                    <TableHead>Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {referrerData.map((referrer) => (
                    <TableRow key={referrer.source}>
                      <TableCell className="font-medium">{referrer.source}</TableCell>
                      <TableCell>{referrer.clicks.toLocaleString()}</TableCell>
                      <TableCell>{referrer.conversions}</TableCell>
                      <TableCell>{referrer.conversionRate}%</TableCell>
                      <TableCell>
                        <Progress value={referrer.conversionRate * 10} className="h-2 w-20" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
