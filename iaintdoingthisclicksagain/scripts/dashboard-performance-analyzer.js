#!/usr/bin/env node

/**
 * Dashboard Performance Analyzer
 * Identifies performance bottlenecks in the dashboard components
 */

const fs = require('fs');
const path = require('path');

const DASHBOARD_DIR = path.join(process.cwd(), 'components', 'dashboard');
const APP_DASHBOARD_DIR = path.join(process.cwd(), 'app', 'dashboard');

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',
    warning: '\x1b[33m',
    error: '\x1b[31m',
    success: '\x1b[32m',
    optimize: '\x1b[35m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

function analyzeFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

function analyzeImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = content.match(/^import.*from.*$/gm) || [];
    
    const heavyImports = imports.filter(imp => 
      imp.includes('recharts') || 
      imp.includes('d3-') ||
      imp.includes('@dnd-kit') ||
      imp.includes('framer-motion')
    );
    
    const chartImports = imports.filter(imp => imp.includes('recharts'));
    
    return {
      totalImports: imports.length,
      heavyImports: heavyImports.length,
      chartImports: chartImports.length,
      hasLazyLoading: content.includes('lazy(') || content.includes('Suspense'),
      hasUseEffect: content.includes('useEffect'),
      hasUseState: content.includes('useState'),
      hasPerformanceWrapper: content.includes('PerformanceWrapper')
    };
  } catch (error) {
    return null;
  }
}

function scanDirectory(dir, results = []) {
  if (!fs.existsSync(dir)) return results;
  
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      scanDirectory(filePath, results);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      const size = analyzeFileSize(filePath);
      const imports = analyzeImports(filePath);
      
      if (imports) {
        results.push({
          file: path.relative(process.cwd(), filePath),
          size,
          ...imports
        });
      }
    }
  }
  
  return results;
}

function generateOptimizationReport(results) {
  log('\n📊 Dashboard Performance Analysis Report', 'optimize');
  log('=' .repeat(50));
  
  // Sort by potential performance impact
  const sortedResults = results.sort((a, b) => {
    const scoreA = (a.heavyImports * 3) + (a.chartImports * 2) + (a.size / 1000);
    const scoreB = (b.heavyImports * 3) + (b.chartImports * 2) + (b.size / 1000);
    return scoreB - scoreA;
  });
  
  log('\n🔍 Files with Potential Performance Issues:');
  
  sortedResults.slice(0, 10).forEach((result, index) => {
    const impact = (result.heavyImports * 3) + (result.chartImports * 2) + (result.size / 1000);
    const impactLevel = impact > 15 ? 'HIGH' : impact > 8 ? 'MEDIUM' : 'LOW';
    const color = impactLevel === 'HIGH' ? 'error' : impactLevel === 'MEDIUM' ? 'warning' : 'info';
    
    log(`\n${index + 1}. ${result.file}`, color);
    log(`   Size: ${(result.size / 1024).toFixed(1)}KB | Impact: ${impactLevel}`, 'info');
    log(`   Heavy imports: ${result.heavyImports} | Chart imports: ${result.chartImports}`, 'info');
    log(`   Has lazy loading: ${result.hasLazyLoading ? '✅' : '❌'}`, result.hasLazyLoading ? 'success' : 'warning');
  });
  
  // Summary statistics
  const totalFiles = results.length;
  const filesWithCharts = results.filter(r => r.chartImports > 0).length;
  const filesWithLazyLoading = results.filter(r => r.hasLazyLoading).length;
  const filesWithHeavyImports = results.filter(r => r.heavyImports > 0).length;
  const totalSize = results.reduce((sum, r) => sum + r.size, 0);
  
  log('\n📈 Summary Statistics:', 'optimize');
  log(`Total dashboard files: ${totalFiles}`);
  log(`Files with charts: ${filesWithCharts} (${((filesWithCharts/totalFiles)*100).toFixed(1)}%)`);
  log(`Files with lazy loading: ${filesWithLazyLoading} (${((filesWithLazyLoading/totalFiles)*100).toFixed(1)}%)`);
  log(`Files with heavy imports: ${filesWithHeavyImports}`);
  log(`Total dashboard size: ${(totalSize / 1024).toFixed(1)}KB`);
  
  return sortedResults;
}

function generateOptimizationSuggestions(results) {
  log('\n💡 Optimization Suggestions:', 'optimize');
  log('=' .repeat(50));
  
  const chartFiles = results.filter(r => r.chartImports > 0 && !r.hasLazyLoading);
  const heavyFiles = results.filter(r => r.heavyImports > 2);
  const largeFiles = results.filter(r => r.size > 10000); // > 10KB
  
  if (chartFiles.length > 0) {
    log('\n🔄 Implement Lazy Loading for Charts:', 'warning');
    chartFiles.slice(0, 5).forEach(file => {
      log(`   • ${file.file} (${file.chartImports} chart imports)`);
    });
    log('   Solution: Use React.lazy() and Suspense for chart components');
  }
  
  if (heavyFiles.length > 0) {
    log('\n📦 Reduce Heavy Imports:', 'warning');
    heavyFiles.slice(0, 5).forEach(file => {
      log(`   • ${file.file} (${file.heavyImports} heavy imports)`);
    });
    log('   Solution: Use dynamic imports or tree shaking');
  }
  
  if (largeFiles.length > 0) {
    log('\n📏 Large Files to Split:', 'warning');
    largeFiles.slice(0, 5).forEach(file => {
      log(`   • ${file.file} (${(file.size/1024).toFixed(1)}KB)`);
    });
    log('   Solution: Split into smaller components');
  }
  
  log('\n🚀 Quick Wins:', 'success');
  log('1. Add lazy loading to chart components');
  log('2. Use React.memo() for static components');
  log('3. Implement virtual scrolling for long lists');
  log('4. Use Suspense boundaries for better UX');
  log('5. Consider code splitting by route');
}

function createOptimizedComponent(filePath) {
  const componentName = path.basename(filePath, '.tsx');
  const optimizedPath = filePath.replace('.tsx', '.optimized.tsx');
  
  log(`\n🔧 Creating optimized version: ${optimizedPath}`, 'optimize');
  
  // This would contain the optimized component template
  // For now, just log the suggestion
  log(`   Suggested optimizations for ${componentName}:`);
  log('   • Add React.memo() wrapper');
  log('   • Implement lazy loading for charts');
  log('   • Add Suspense boundaries');
  log('   • Optimize re-renders with useCallback');
}

// Main execution
function main() {
  log('🔍 Analyzing Dashboard Performance...', 'optimize');
  
  const results = [
    ...scanDirectory(DASHBOARD_DIR),
    ...scanDirectory(APP_DASHBOARD_DIR)
  ];
  
  if (results.length === 0) {
    log('No dashboard files found to analyze.', 'warning');
    return;
  }
  
  const sortedResults = generateOptimizationReport(results);
  generateOptimizationSuggestions(results);
  
  // Create optimization commands
  log('\n⚡ Quick Performance Commands:', 'success');
  log('bun run dev:fast     # Start with performance optimizations');
  log('bun run analyze:bundle # Analyze bundle size');
  log('bun run cache:clean   # Clear development cache');
  
  log('\n✨ Analysis complete! Check the suggestions above.', 'success');
}

if (require.main === module) {
  main();
}

module.exports = { analyzeFileSize, analyzeImports, scanDirectory };
