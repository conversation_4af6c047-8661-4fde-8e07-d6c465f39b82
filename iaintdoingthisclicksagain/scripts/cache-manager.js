#!/usr/bin/env node

/**
 * Cache Management Script for Next.js Development
 * Handles webpack cache corruption and optimization
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CACHE_DIRS = [
  '.next',
  'node_modules/.cache',
  '.turbo',
  '.bun-cache',
];

const TEMP_DIRS = [
  'tmp',
  '.tmp',
  '.temp',
];

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    clean: '🧹'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      log(`Removed: ${dirPath}`, 'clean');
      return true;
    } catch (error) {
      log(`Failed to remove ${dirPath}: ${error.message}`, 'error');
      return false;
    }
  }
  return false;
}

function getDirectorySize(dirPath) {
  if (!fs.existsSync(dirPath)) return 0;
  
  let size = 0;
  try {
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    for (const file of files) {
      const filePath = path.join(dirPath, file.name);
      if (file.isDirectory()) {
        size += getDirectorySize(filePath);
      } else {
        size += fs.statSync(filePath).size;
      }
    }
  } catch (error) {
    // Ignore errors for inaccessible files
  }
  return size;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeCacheHealth() {
  log('Analyzing cache health...', 'info');
  
  const analysis = {
    totalSize: 0,
    directories: {},
    issues: []
  };
  
  for (const dir of CACHE_DIRS) {
    if (fs.existsSync(dir)) {
      const size = getDirectorySize(dir);
      analysis.totalSize += size;
      analysis.directories[dir] = {
        size,
        exists: true,
        readable: true
      };
      
      // Check for corruption indicators
      if (dir === '.next') {
        const cacheDir = path.join(dir, 'cache');
        if (fs.existsSync(cacheDir)) {
          try {
            const cacheFiles = fs.readdirSync(cacheDir);
            if (cacheFiles.length > 1000) {
              analysis.issues.push(`Large cache directory: ${cacheFiles.length} files in ${cacheDir}`);
            }
          } catch (error) {
            analysis.issues.push(`Cannot read cache directory: ${cacheDir}`);
          }
        }
      }
    } else {
      analysis.directories[dir] = {
        size: 0,
        exists: false,
        readable: false
      };
    }
  }
  
  return analysis;
}

function cleanCache(options = {}) {
  const { force = false, selective = false } = options;
  
  log('Starting cache cleanup...', 'clean');
  
  const analysis = analyzeCacheHealth();
  log(`Current cache size: ${formatBytes(analysis.totalSize)}`, 'info');
  
  if (analysis.issues.length > 0) {
    log('Cache issues detected:', 'warning');
    analysis.issues.forEach(issue => log(`  - ${issue}`, 'warning'));
  }
  
  let cleaned = 0;
  let totalCleaned = 0;
  
  for (const dir of CACHE_DIRS) {
    if (selective && dir === 'node_modules/.cache') {
      // Keep node_modules cache unless forced
      if (!force) {
        log(`Skipping ${dir} (use --force to clean)`, 'info');
        continue;
      }
    }
    
    if (removeDirectory(dir)) {
      const size = analysis.directories[dir]?.size || 0;
      totalCleaned += size;
      cleaned++;
    }
  }
  
  // Clean temporary directories
  for (const dir of TEMP_DIRS) {
    removeDirectory(dir);
  }
  
  log(`Cleaned ${cleaned} cache directories`, 'success');
  log(`Freed up ${formatBytes(totalCleaned)} of disk space`, 'success');
  
  return { cleaned, totalCleaned };
}

function optimizeCache() {
  log('Optimizing cache configuration...', 'info');
  
  // Create optimized cache directories
  const cacheDir = '.next/cache';
  if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true });
    log(`Created cache directory: ${cacheDir}`, 'success');
  }
  
  // Set proper permissions (Unix-like systems)
  if (process.platform !== 'win32') {
    try {
      execSync(`chmod 755 ${cacheDir}`);
      log('Set cache directory permissions', 'success');
    } catch (error) {
      log('Could not set cache permissions', 'warning');
    }
  }
  
  log('Cache optimization complete', 'success');
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'analyze';
  
  switch (command) {
    case 'clean':
      const force = args.includes('--force');
      const selective = !args.includes('--all');
      cleanCache({ force, selective });
      optimizeCache();
      break;
      
    case 'analyze':
      const analysis = analyzeCacheHealth();
      console.log('\n📊 Cache Analysis Report:');
      console.log(`Total cache size: ${formatBytes(analysis.totalSize)}`);
      console.log('\nDirectory breakdown:');
      for (const [dir, info] of Object.entries(analysis.directories)) {
        const status = info.exists ? `${formatBytes(info.size)}` : 'Not found';
        console.log(`  ${dir}: ${status}`);
      }
      if (analysis.issues.length > 0) {
        console.log('\n⚠️  Issues found:');
        analysis.issues.forEach(issue => console.log(`  - ${issue}`));
        console.log('\nRun "node scripts/cache-manager.js clean" to resolve issues.');
      } else {
        console.log('\n✅ No cache issues detected.');
      }
      break;
      
    case 'optimize':
      optimizeCache();
      break;
      
    default:
      console.log(`
Usage: node scripts/cache-manager.js <command>

Commands:
  analyze   - Analyze cache health and size
  clean     - Clean corrupted cache files
  optimize  - Optimize cache configuration

Options:
  --force   - Force clean all caches including node_modules
  --all     - Clean all cache directories (default: selective)

Examples:
  node scripts/cache-manager.js analyze
  node scripts/cache-manager.js clean
  node scripts/cache-manager.js clean --force
      `);
  }
}

if (require.main === module) {
  main();
}

module.exports = { cleanCache, analyzeCacheHealth, optimizeCache };
