import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { ErrorBoundary } from "@/components/debug/ErrorBoundary"
import { DebugPanelWrapper } from "@/components/debug/DebugPanelWrapper"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Social Media Marketing Dashboard",
  description: "Comprehensive dashboard for social media marketers with debug tools",
  generator: 'v0.dev'
}

// Disable API debugging for better performance
// if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
//   import('@/lib/debug/api-debug')
// }

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange={false}>
          <ErrorBoundary>
            {children}
            <Toaster />
            <DebugPanelWrapper />
          </ErrorBoundary>
        </ThemeProvider>
      </body>
    </html>
  )
}
