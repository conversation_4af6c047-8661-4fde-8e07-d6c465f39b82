import { useDebugValue as useReactDebugValue, useRef, useEffect } from 'react';
import { logger } from './logger';

export function useDebugHook<T>(value: T, name: string, componentName?: string): void {
  // Always call hooks but return early if disabled
  const prevValue = useRef<T>(value);

  // Only run in development with debug enabled
  const isEnabled = process.env.NODE_ENV === 'development' && process.env.ENABLE_PERFORMANCE_LOGGING === 'true';

  // Use React's built-in debug value only if enabled
  if (isEnabled) {
    useReactDebugValue(value);
  }

  useEffect(() => {
    if (!isEnabled) return;

    // Log when value changes
    if (JSON.stringify(prevValue.current) !== JSON.stringify(value)) {
      logger.debug(
        `${name} changed in ${componentName || 'component'}`,
        {
          from: prevValue.current,
          to: value
        },
        'useDebugHook'
      );
      prevValue.current = value;
    }
  }, [value, name, componentName, isEnabled]);
}

export function useRenderCount(componentName: string): number {
  const renderCount = useRef(0);

  // Always increment render count
  renderCount.current += 1;

  // Only track in development with debug enabled
  const isEnabled = process.env.NODE_ENV === 'development' && process.env.ENABLE_PERFORMANCE_LOGGING === 'true';

  useEffect(() => {
    if (!isEnabled) return;

    // Only log occasionally to prevent spam
    if (renderCount.current % 10 === 0) {
      logger.debug(
        `${componentName} rendered`,
        { count: renderCount.current },
        'useRenderCount'
      );
    }
  }, [componentName, isEnabled]);

  return isEnabled ? renderCount.current : 0;
}