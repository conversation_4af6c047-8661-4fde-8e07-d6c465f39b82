export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'performance';

export interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: number;
  source?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 500;

  constructor() {
    this.logs = [];
  }

  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    // Skip debug and info logs in development to reduce noise
    if (process.env.NODE_ENV === 'development' && (level === 'debug' || level === 'info')) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: Date.now(),
      source
    };

    // Add to internal log
    this.logs.unshift(entry);

    // Trim logs if they exceed max size
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Only dispatch event in browser for important logs
    if (typeof window !== 'undefined' && (level === 'error' || level === 'warn')) {
      window.dispatchEvent(new CustomEvent('debug-log', { detail: entry }));
    }

    // Console output only for errors and warnings
    if (process.env.NODE_ENV === 'development' && (level === 'error' || level === 'warn')) {
      const emoji = this.getLogEmoji(level);
      if (level === 'error') {
        console.error(`${emoji} ${message}`, data || '');
      } else if (level === 'warn') {
        console.warn(`${emoji} ${message}`, data || '');
      }
    }
  }

  private getLogEmoji(level: LogLevel): string {
    switch (level) {
      case 'debug': return '🔍';
      case 'info': return 'ℹ️';
      case 'warn': return '⚠️';
      case 'error': return '❌';
      case 'performance': return '⚡';
      default: return '';
    }
  }

  debug(message: string, data?: any, source?: string): void {
    this.log('debug', message, data, source);
  }

  info(message: string, data?: any, source?: string): void {
    this.log('info', message, data, source);
  }

  warn(message: string, data?: any, source?: string): void {
    this.log('warn', message, data, source);
  }

  error(message: string, data?: any, source?: string): void {
    this.log('error', message, data, source);
  }

  performance(message: string, startTime: number, source?: string): void {
    const duration = performance.now() - startTime;
    this.log('performance', `${message} (${duration.toFixed(2)}ms)`, { duration }, source);
  }

  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }
}

export const logger = new Logger();